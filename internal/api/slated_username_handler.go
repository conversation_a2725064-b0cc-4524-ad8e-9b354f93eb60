package api

import (
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/gofiber/fiber/v2"
)

// SlatedUsernameHandler exposes an endpoint to verify if a username is present
// in the slated_usernames.csv list.
//
// It returns a plain JSON boolean: true if the username is present, false
// otherwise.
//
// Example request:
//
//	GET /api/v1/register/isReserved/alexsmith  ->  true
//
// If the username path parameter is missing a 400 Bad Request is returned.
// On internal errors a 500 Internal Server Error is returned.
//
// swagger:route GET /register/isReserved/{username} slatedUsernames checkReservedUsername
// Returns true if the username is slated.
// responses:
//
//	200: boolean
//	400: ErrorResponse
//	500: ErrorResponse
type SlatedUsernameHandler struct {
	checker *utils.SlatedUsernames
}

func NewSlatedUsernameHandler(checker *utils.SlatedUsernames) *SlatedUsernameHandler {
	return &SlatedUsernameHandler{checker: checker}
}

func (h *SlatedUsernameHandler) IsReserved(c *fiber.Ctx) error {
	username := c.Params("username")
	if username == "" {
		return fiber.NewError(fiber.StatusBadRequest, "username path parameter is required")
	}

	exists, err := h.checker.Contains(username)
	if err != nil {
		slog.Error("failed to check slated usernames", slog.Any("error", err))
		return fiber.NewError(fiber.StatusInternalServerError, "internal error")
	}

	return c.JSON(exists)
}
