package postgres

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"slices"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
)

// Assert interface implementation.
var _ domain.BetRepository = (*BetRepository)(nil)

// BetRepository is a struct that will implement the BetRepository interface.
type BetRepository struct {
	db       *gorm.DB
	vipTiers domain.VIPTiers
}

// NewBetRepository creates a new instance of BetRepository.
func NewBetRepository(db *gorm.DB, vipTiers domain.VIPTiers) *BetRepository {
	return &BetRepository{db: db, vipTiers: vipTiers}
}

// UpsertBet updates a bet if it exists, otherwise it creates a new one.
func (r *BetRepository) UpsertBet(ctx context.Context, bet *domain.Bet) (*domain.Bet, error) {
	tx := r.db.WithContext(ctx).Exec("SET session_replication_role = 'replica'")

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("set session_replication_role: %w", err)
	}
	row := betToRow(bet)

	tx = r.db.
		WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns: []clause.Column{{Name: "external_id"}},
				DoUpdates: slices.Concat(
					clause.AssignmentColumns([]string{
						"updated_at",
						"game_id",
						"user_id",
						"bet_type",
						"currency",
						"converted_to",
						"external_id",
						"ghost_mode",
						"hidden_boolean",
						"round_status",
						"time",
						"event",
					}),
					// GORM doesn't exclude nil/zero values when upserting.
					// See: https://github.com/go-gorm/gorm/issues/4780
					clause.Assignments(map[string]any{
						"bet_amount": gorm.Expr(`
                            CASE 
                                WHEN bets.bet_amount IS NOT NULL AND excluded.bet_amount IS NOT NULL 
                                THEN bets.bet_amount + excluded.bet_amount 
                                ELSE COALESCE(excluded.bet_amount, bets.bet_amount) 
                            END
                            `),
						"actual_bet_amount": gorm.Expr(`
                            CASE 
                                WHEN bets.actual_bet_amount IS NOT NULL AND excluded.actual_bet_amount IS NOT NULL 
                                THEN bets.actual_bet_amount + excluded.actual_bet_amount 
                                ELSE COALESCE(excluded.actual_bet_amount, bets.actual_bet_amount) 
                            END
                            `),
						"payout": gorm.Expr(`
                            CASE 
                                WHEN bets.payout IS NOT NULL AND excluded.payout IS NOT NULL 
                                THEN bets.payout + excluded.payout 
                                ELSE COALESCE(excluded.payout, bets.payout) 
                            END
                            `),
						"actual_win_amount": gorm.Expr(`
                            CASE 
                                WHEN bets.actual_win_amount IS NOT NULL AND excluded.actual_win_amount IS NOT NULL 
                                THEN bets.actual_win_amount + excluded.actual_win_amount 
                                ELSE COALESCE(excluded.actual_win_amount, bets.actual_win_amount) 
                            END
                        `),
						"odds": gorm.Expr(`COALESCE(excluded.odds, bets.odds)`),
					}),
				),
			},
			clause.Returning{},
		).
		Create(&row)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("upsert bet: %w", err)
	}

	return rowToBetForUpsertPtr(row), nil
}

func (r *BetRepository) GetBets(
	ctx context.Context,
	params *domain.GetBetsParams,
) (*domain.Bets, error) {
	rows := []betWithStats{}

	tx := r.buildBetsJoin(ctx)

	if !params.IncludeBetsWithoutGame {
		tx = tx.Where("games.name IS NOT NULL")
	}

	if params.UserExternalID != nil {
		tx = tx.Where("users.external_id = ?", params.UserExternalID)
	}

	if params.PayoutOver != nil {
		tx = tx.Where("payout > ?", params.PayoutOver)
	}

	if params.FilterBetsByGame != nil && *params.FilterBetsByGame {
		tx = tx.Where("games.cms_game_id NOT IN ?", utils.FilteredCMSGameIDs)
	}

	if params.BetType != nil {
		tx = tx.Where("bets.bet_type = ?", *params.BetType)
	}

	tx.
		Order(getOrderBy(params.OrderParams)).
		Limit(params.PageSize).
		Offset(calculateOffset(params.PagingParams)).
		Find(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find bets: %w", err)
	}

	return r.rowsToBets(rows, params.PageNumber, params.PageSize), nil
}

func (r *BetRepository) GetBetByID(ctx context.Context, id uuid.UUID) (*domain.Bet, error) {
	var row betWithStats

	tx := r.buildBetsJoin(ctx).
		Where("bets.id = ?", id).
		First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find bet by ID: %w", err)
	}

	return r.rowToBetPtr(row), nil
}

func (r *BetRepository) GetBetsByUserID(
	ctx context.Context,
	params *domain.GetBetsByUserIDParams,
) (*domain.UserBets, error) {
	var count int64
	rows := []betWithStats{}

	tx := r.buildBetsJoin(ctx).
		Where("users.external_id = ? AND bet_type = ?", params.UserID, params.Type)

	if params.IncludeBetsWithoutGame {
		tx = tx.Where("games.name IS NOT NULL")
	}

	if params.From != nil {
		tx = tx.Where("bets.time >= ?", *params.From)
	}

	if params.To != nil {
		tx = tx.Where("bets.time <= ?", *params.To)
	}

	if *params.Type == "SPORTS" && params.Status != nil {
		switch *params.Status {
		case "open":
			tx = tx.Where("bets.payout IS NULL")
		case "settled":
			tx = tx.Where("bets.payout IS NOT NULL")
		}
	}

	if params.Result != nil {
		switch *params.Result {
		case "win":
			tx = tx.Where("bets.multiplier > 1")
		case "lose":
			tx = tx.Where("bets.multiplier < 1")
		}
	}

	if len(params.Currency) > 0 {
		tx = tx.Where("bets.currency IN ?", params.Currency)
	}

	var totalWagered, totalWon float64

	txDebit := tx.Session(&gorm.Session{}).Select("COALESCE(SUM(bets.bet_amount), 0)").
		Scan(&totalWagered)

	if err := txDebit.Error; err != nil {
		return nil, fmt.Errorf("calculate total debit: %w", err)
	}

	txCredit := tx.Session(&gorm.Session{}).Select("COALESCE(SUM(bets.payout), 0)").
		Scan(&totalWon)

	if err := txCredit.Error; err != nil {
		return nil, fmt.Errorf("calculate total credit: %w", err)
	}

	tx.
		Count(&count).
		Order(getOrderBy(params.OrderParams)).
		Limit(params.PageSize).
		Offset(calculateOffset(params.PagingParams)).
		Find(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find bets by user ID: %w", err)
	}

	details := domain.Details{
		TotalCredit: totalWon,
		TotalDebit:  totalWagered,
	}

	return r.rowsToUserBets(rows, count, params.PageNumber, params.PageSize, details), nil
}

func (r *BetRepository) buildBetsJoin(ctx context.Context) *gorm.DB {
	return r.db.
		WithContext(ctx).
		Model(&bet{}).
		Select("bets.*, games.*, users.*, bs.*, cs.*").
		Joins("INNER JOIN games ON bets.game_id = games.id").
		Joins("INNER JOIN users ON bets.user_id = users.id").
		Joins("INNER JOIN users_bets_summary bs ON users.id = bs.user_id").
		Joins("INNER JOIN users_coins_summary cs ON users.id = cs.user_id").
		Where("bets.round_status = ?", "completed")
}

func (r *BetRepository) GetLatestBetsByUserId(ctx context.Context, externalID string) ([]domain.Bet, error) {
	var user struct {
		ID string
	}

	if err := r.db.WithContext(ctx).
		Table("users").
		Select("id").
		Where("external_id = ?", externalID).
		First(&user).Error; err != nil {
		return nil, fmt.Errorf("find user by external ID: %w", err)
	}

	var rows []bet
	tx := r.db.WithContext(ctx).
		Preload("Game").
		Preload("User").
		Where("user_id = ?", user.ID).
		Order("time DESC").
		Limit(100).
		Find(&rows)

	if err := tx.Error; err != nil {
		return nil, fmt.Errorf("find latest bets by user ID: %w", err)
	}

	bets := make([]domain.Bet, len(rows))
	for i, row := range rows {
		bets[i] = rowToDomainBet(row)
	}

	return bets, nil

}

func (r *BetRepository) ExportBets(ctx context.Context, params *domain.ExportBetsParams) ([]domain.Bet, error) {
	betRows := []bet{}

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		userRow := &user{}

		tx.
			Select("id").
			First(&userRow, "external_id = ?", params.UserExternalID)

		if err := tx.Error; err != nil {
			return fmt.Errorf("find user: %w", err)
		}

		tx.
			Preload("Game").
			Joins("LEFT JOIN games ON bets.game_id = games.id").
			Where("games.name IS NOT NULL").
			Where("bets.time BETWEEN ? AND ?", params.StartDate, params.EndDate).
			Where("bets.user_id = ?", userRow.ID).
			Order("bets.time DESC").
			Find(&betRows)

		if err := tx.Error; err != nil {
			return fmt.Errorf("find bets: %w", err)
		}

		return nil
	})
	if err != nil {
		slog.Error("Failed to export bets transaction",
			slog.String("user_external_id", params.UserExternalID),
			slog.Any("start_date", params.StartDate),
			slog.Any("end_date", params.EndDate),
			slog.Any("error", err))
		return nil, fmt.Errorf("export bets transaction: %w", err)
	}

	return rowsToBetsForExport(betRows), nil
}

func (r *BetRepository) rowToBetPtr(row betWithStats) *domain.Bet {
	return rowToBetPtr(row, r.vipTiers)
}

func (r *BetRepository) rowsToBets(rows []betWithStats, pageNum, pageSize int) *domain.Bets {
	return rowsToBets(rows, pageNum, pageSize, r.vipTiers)
}

func (r *BetRepository) rowsToUserBets(rows []betWithStats, count int64, pageNum, pageSize int, details domain.Details) *domain.UserBets {
	return rowsToUserBets(rows, count, pageNum, pageSize, r.vipTiers, details)
}

func (r *BetRepository) GetBetByExternalID(ctx context.Context, externalID string) (*domain.Bet, error) {
	var row betWithStats

	tx := r.buildBetsJoin(ctx).
		Where("bets.external_id = ?", externalID).
		First(&row)

	if err := tx.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, domain.ErrResourceNotFound
		}

		return nil, fmt.Errorf("find bet by external ID: %w", err)
	}

	return r.rowToBetPtr(row), nil
}

func (r *BetRepository) ShareUserBetByBetId(ctx context.Context, externalID string, betID uuid.UUID) (domain.Bet, bool, error) {
	var user struct {
		ID string
	}

	if err := r.db.WithContext(ctx).
		Table("users").
		Select("id").
		Where("external_id = ?", externalID).
		First(&user).Error; err != nil {
		return domain.Bet{}, false, fmt.Errorf("find user by external ID: %w", err)
	}

	var row bet
	tx := r.db.WithContext(ctx).
		Preload("Game").
		Where("user_id = ? AND external_id = ?", user.ID, betID).
		First(&row)

	if err := tx.Error; err != nil {
		return domain.Bet{}, false, domain.ErrBetNotAssociatedWithUser
	}

	var mt_games = []int{1020, 1021, 1022, 1023, 1024, 1025, 1026}
	gameType := false
	if slices.Contains(mt_games, int(row.Game.CMSGameID)) {
		gameType = true
	}

	return rowToDomainBet(row), gameType, nil
}

func (r *BetRepository) GetWageredAmountByTypeForXDays(ctx context.Context, userID string, days int) (float64, error) {
	var totalWagered float64

	interval := fmt.Sprintf("%d days", days)

	query := `
		SELECT COALESCE(SUM(bet_amount), 0) as total_wagered
		FROM bets b
		JOIN users u ON b.user_id = u.id 
		WHERE u.external_id = ?
		AND b.time >= NOW() - ?::INTERVAL
		AND b.bet_type IN ('CASINO', 'SPORTS')
	`

	err := r.db.WithContext(ctx).Raw(query, userID, interval).Scan(&totalWagered).Error
	if err != nil {
		slog.Error("Failed to query wagered amounts",
			slog.String("user_id", userID),
			slog.String("interval", interval),
			slog.Any("error", err))
		return 0, fmt.Errorf("query wagered amounts: %v", err)
	}

	return totalWagered, nil
}
