package service

import (
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"sync"
	"time"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka/schemas"
	"github.com/google/uuid"
)

// Assert interface implementation.
var _ domain.MessageHandler = (*BetMessageHandler)(nil)

type BetMessageHandler struct {
	betRepository    domain.BetRepository
	gameRepository   domain.GameRepository
	userRepository   domain.UserRepository
	boostRepository  domain.BoostRepository
	hubBets          chan<- domain.Bet
	elantilWagering  domain.ElantilWageringClient
	betBuffer        *BetBuffer
	priceStore       *PriceStore
	xpWorkerPool     *XPWorkerPool
	betSimulator     domain.BetSimulatorService
	simulationConfig domain.SimulationConfig
}

type BetEntry struct {
	timestamp time.Time
}

type BetBuffer struct {
	buffer        []domain.Bet
	size          int
	head          int
	tail          int
	count         int
	lock          sync.Mutex
	sendChan      chan<- domain.Bet
	stopChan      chan struct{}
	processedBets map[uuid.UUID]BetEntry
	ttl           time.Duration
}

func NewBetBuffer(size int, sendChan chan<- domain.Bet) *BetBuffer {
	bb := &BetBuffer{
		buffer:        make([]domain.Bet, size),
		size:          size,
		sendChan:      sendChan,
		stopChan:      make(chan struct{}),
		processedBets: make(map[uuid.UUID]BetEntry),
		ttl:           5 * time.Minute,
	}

	go bb.processBets()
	go bb.cleanupExpiredBets()
	go bb.clearBuffer()

	return bb
}

func NewBetMessageHandler(
	betRepository domain.BetRepository,
	gameRepository domain.GameRepository,
	userRepository domain.UserRepository,
	boostRepository domain.BoostRepository,
	hubBets chan domain.Bet,
	elantilWagering domain.ElantilWageringClient,
	priceStore *PriceStore,
) *BetMessageHandler {
	xpWorkerPool := NewXPWorkerPool(10, 1000, userRepository)
	xpWorkerPool.Start()

	handler := &BetMessageHandler{
		betRepository:    betRepository,
		gameRepository:   gameRepository,
		userRepository:   userRepository,
		boostRepository:  boostRepository,
		hubBets:          hubBets,
		elantilWagering:  elantilWagering,
		betBuffer:        NewBetBuffer(10000, hubBets),
		priceStore:       priceStore,
		xpWorkerPool:     xpWorkerPool,
		betSimulator:     nil,                       // Will be set later if needed
		simulationConfig: domain.SimulationConfig{}, // Will be set from config when simulation is enabled
	}

	return handler
}

func (h *BetMessageHandler) Shutdown() {
	// Stop simulation if active
	if h.betSimulator != nil {
		if err := h.betSimulator.StopSimulation(); err != nil {
			slog.Error("Failed to stop slated bet simulation during shutdown", "error", err)
		} else {
			slog.Info("Slated bet simulation stopped during shutdown")
		}
	}

	if h.xpWorkerPool != nil {
		h.xpWorkerPool.Stop()
	}
	if h.betBuffer != nil {
		h.betBuffer.Stop()
	}
}

func (h *BetMessageHandler) HandleMessage(message []byte) error {
	var value schemas.TransactionMessage
	if err := json.Unmarshal(message, &value); err != nil {
		return err
	}

	slog.Info("Successfully decoded bet transaction",
		slog.Any("message", value))

	return h.handleTransaction(context.Background(), &value.Payload.Current)
}

func (h *BetMessageHandler) handleTransaction(
	ctx context.Context,
	message *schemas.TransactionPayload,
) error {
	if !message.IsBetTrans() {
		return nil
	}

	userGame, err := h.userRepository.GetUserAndGameByExternalId(ctx, message.GetUserID(), message.GetGameID())
	if err != nil {
		slog.Error("Failed to get user and game by external ID", "error", err)
		return err
	}

	symbol, cryptoAmount := message.ExtractCurrencySymbol()

	toUSDValue := h.priceStore.GetPriceValue(symbol)

	if message.IsBetSports() {
		parsedBet, err := message.ParseBet(userGame.GameID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
		if err != nil {
			slog.Error("Failed to parse bet in first case", "error", err)
			return err
		}

		_, err = h.betRepository.UpsertBet(ctx, parsedBet)
		if err != nil {
			slog.Error("Failed to upsert bet", "error", err)
			return err
		}
	} else {
		switch {
		case userGame.GameID != uuid.Nil:
			if !message.IsBetCompleted() {
				break
			}
			parsedBet, err := message.ParseBet(userGame.GameID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
			if err != nil {
				slog.Error("Failed to parse bet in first case", "error", err)
				return err
			}

			bet, err := h.betRepository.UpsertBet(ctx, parsedBet)
			if err != nil {
				slog.Error("Failed to upsert bet", "error", err)
				return err
			}

			if !message.IsBetCredit() {
				logBet(bet, slog.LevelInfo, "Bet not sent to hub because it's not completed")
				break
			}

			if bet.BetAmount == nil || *bet.BetAmount == 0 {
				logBet(bet, slog.LevelInfo, "Bet not sent to hub because amount is zero")
				break
			}

			if userGame.GhostMode {
				userGame.UserName = "Hidden"
			}

			bet.User = domain.User{
				ID:        userGame.UserID,
				UserName:  userGame.UserName,
				VIPStatus: userGame.ElantilVipStatus,
			}

			bet.Game = domain.Game{
				ID:          userGame.GameID,
				ExternalID:  userGame.ExternalID,
				Name:        &userGame.Name,
				CMSGameID:   userGame.CmsGameID,
				Slug:        &userGame.Slug,
				ThumbnailID: &userGame.ThumbnailID,
			}

			if err := h.betBuffer.Add(*bet); err != nil {
				if err.Error() == "buffer is full" {
					slog.Warn("Bet dropped - buffer full",
						"betId", bet.ID,
						"externalId", bet.ExternalID,
						"user", bet.User.UserName,
						"game", *bet.Game.Name,
						"amount", bet.BetAmount,
						"reason", "buffer_full",
						"bufferSize", h.betBuffer.size)
				} else {
					slog.Error("Failed to add bet to buffer",
						"error", err,
						"betId", bet.ID,
						"externalId", bet.ExternalID)
				}
				return err
			}

		case userGame.GameID == uuid.Nil:
			if !message.IsBetCompleted() {
				slog.Info("Bet not sent to hub because it's not completed", "bet_external_id", message.BatchExternalID, "game_external_id", userGame.ExternalID, "user_external_id", userGame.UserID)
				break
			}
			game, err := h.gameRepository.CreateGame(ctx, message.ParseGame())
			if err != nil {
				slog.Error("Failed to create game", "error", err)
				return err
			}

			parsedBet, err := message.ParseBet(game.ID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
			if err != nil {
				slog.Error("Failed to parse bet in second case", "error", err)
				return err
			}

			bet, err := h.betRepository.UpsertBet(ctx, parsedBet)
			if err != nil {
				slog.Error("Failed to upsert bet", "error", err)
				return err
			}

			if !message.IsBetCredit() {
				logBet(bet, slog.LevelInfo, "Bet not sent to hub because it's not completed")
				break
			}

			if bet.BetAmount == nil || *bet.BetAmount == 0 {
				logBet(bet, slog.LevelInfo, "Bet not sent to hub because amount is zero")
				break
			}

			if userGame.GhostMode {
				userGame.UserName = "Hidden"
			}

			bet.User = domain.User{
				ID:        userGame.UserID,
				UserName:  userGame.UserName,
				VIPStatus: userGame.ElantilVipStatus,
			}

			bet.Game = domain.Game{
				ID:          userGame.GameID,
				ExternalID:  userGame.ExternalID,
				Name:        &userGame.Name,
				CMSGameID:   userGame.CmsGameID,
				Slug:        &userGame.Slug,
				ThumbnailID: &userGame.ThumbnailID,
			}
			if err := h.betBuffer.Add(*bet); err != nil {
				if err.Error() == "buffer is full" {
					slog.Warn("Bet dropped - buffer full",
						"betId", bet.ID,
						"externalId", bet.ExternalID,
						"user", bet.User.UserName,
						"game", *bet.Game.Name,
						"amount", bet.BetAmount,
						"reason", "buffer_full",
						"bufferSize", h.betBuffer.size)
				} else {
					slog.Error("Failed to add bet to buffer",
						"error", err,
						"betId", bet.ID,
						"externalId", bet.ExternalID)
				}
				return err
			}
		}
	}
	if message.WagerDebitOnly() {
		bet, err := message.ParseBet(userGame.GameID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
		if err != nil {
			slog.Error("Failed to parse bet for XP update", "error", err)
		} else {
			h.xpWorkerPool.EnqueueXPUpdate(message.GetUserID(), bet.BetAmount, message.IsBetSports())
		}
	}
	return nil
}

func (bb *BetBuffer) remove() (domain.Bet, bool) {
	if bb.count == 0 {
		return domain.Bet{}, false
	}

	bet := bb.buffer[bb.tail]
	bb.buffer[bb.tail] = domain.Bet{}
	bb.tail = (bb.tail + 1) % bb.size
	bb.count--

	return bet, true
}

func (bb *BetBuffer) cleanupExpiredBets() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-bb.stopChan:
			return
		case <-ticker.C:
			bb.lock.Lock()
			now := time.Now()
			for betID, entry := range bb.processedBets {
				if now.Sub(entry.timestamp) > bb.ttl {
					delete(bb.processedBets, betID)
				}
			}
			bb.lock.Unlock()
		}
	}
}

func (bb *BetBuffer) clearBuffer() {
	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-bb.stopChan:
			return
		case <-ticker.C:
			bb.lock.Lock()

			if bb.count > int(float64(bb.size)*0.8) {
				slog.Warn("BetBuffer clearing pending bets due to high occupancy",
					"pendingBets", bb.count,
					"trackedBets", len(bb.processedBets),
					"bufferSize", bb.size,
					"occupancyPercent", int(float64(bb.count)/float64(bb.size)*100))

				bb.head = 0
				bb.tail = 0
				bb.count = 0
				bb.buffer = make([]domain.Bet, bb.size)
			}

			bb.lock.Unlock()
		}
	}
}

func (bb *BetBuffer) exists(betID uuid.UUID) bool {
	entry, exists := bb.processedBets[betID]
	if !exists {
		return false
	}

	if time.Since(entry.timestamp) > bb.ttl {
		delete(bb.processedBets, betID)
		return false
	}

	return true
}

func (bb *BetBuffer) processBets() {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-bb.stopChan:
			return
		case <-ticker.C:
			bb.lock.Lock()
			if bet, ok := bb.remove(); ok {
				bb.lock.Unlock()

				if bb.sendChan != nil {
					select {
					case bb.sendChan <- bet:
					default:
						slog.Warn("Websocket channel full, skipping bet",
							"betId", bet.ID,
							"externalId", bet.ExternalID,
							"user", bet.User.UserName,
							"amount", bet.BetAmount,
							"reason", "websocket_channel_full")
					}
				}
			} else {
				bb.lock.Unlock()
			}
		}
	}
}

func (bb *BetBuffer) Add(bet domain.Bet) error {
	bb.lock.Lock()
	defer bb.lock.Unlock()

	if bb.count == bb.size {
		return errors.New("buffer is full")
	}

	if bb.exists(bet.ID) {
		return nil
	}

	bb.buffer[bb.head] = bet
	bb.processedBets[bet.ID] = BetEntry{
		timestamp: time.Now(),
	}
	bb.head = (bb.head + 1) % bb.size
	bb.count++
	return nil
}

func (bb *BetBuffer) Stop() {
	close(bb.stopChan)
}

func (h *BetMessageHandler) HandleRecoveryMessage(message []byte) error {
	var value schemas.TransactionMessage
	if err := json.Unmarshal(message, &value); err != nil {
		return err
	}

	slog.Info("Succesfully decoded bet transaction", slog.Any("message", value))
	return h.handleRecoveryTransaction(context.Background(), &value.Payload.Current)
}

func (h *BetMessageHandler) handleRecoveryTransaction(
	ctx context.Context,
	message *schemas.TransactionPayload,
) error {
	if !message.IsBetTrans() {
		return nil
	}

	userGame, err := h.userRepository.GetUserAndGameByExternalId(ctx, message.GetUserID(), message.GetGameID())
	if err != nil {
		slog.Error("Failed to get user and game by external ID", "error", err)
		return err
	}

	symbol, cryptoAmount := message.ExtractCurrencySymbol()

	toUSDValue := h.priceStore.GetPriceValue(symbol)

	if message.IsBetSports() {
		parsedBet, err := message.ParseBet(userGame.GameID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
		if err != nil {
			slog.Error("Failed to parse bet in first case", "error", err)
			return err
		}

		_, err = h.betRepository.UpsertBet(ctx, parsedBet)
		if err != nil {
			slog.Error("Failed to upsert bet", "error", err)
			return err
		}
	} else {
		switch {
		case userGame.GameID != uuid.Nil && userGame.UserID != uuid.Nil && userGame.Name != "":
			parsedBet, err := message.ParseBet(userGame.GameID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
			if err != nil {
				slog.Error("Failed to parse bet in first case", "error", err)
				return err
			}

			_, err = h.betRepository.UpsertBet(ctx, parsedBet)
			if err != nil {
				slog.Error("Failed to upsert bet", "error", err)
				return err
			}

		case userGame.GameID == uuid.Nil:
			game, err := h.gameRepository.CreateGame(ctx, message.ParseGame())
			if err != nil {
				slog.Error("Failed to create game", "error", err)
				return err
			}

			parsedBet, err := message.ParseBet(game.ID, userGame.UserID, symbol, toUSDValue, cryptoAmount)
			if err != nil {
				slog.Error("Failed to parse bet in second case", "error", err)
				return err
			}

			_, err = h.betRepository.UpsertBet(ctx, parsedBet)
			if err != nil {
				slog.Error("Failed to upsert bet", "error", err)
				return err
			}
		}
	}

	return nil
}

func (h *BetMessageHandler) HandleUpsertXPUpdate(ctx context.Context, userExternalID string, betAmount *float64, isSportsBet bool) {
	xpToAdd := *betAmount

	if isSportsBet {
		xpToAdd = xpToAdd * 3
	}
	_, err := h.userRepository.UpdateUpsertUserXP(ctx, userExternalID, xpToAdd)
	if err != nil {
		return
	}
}

func logBet(bet *domain.Bet, level slog.Level, msg string, args ...any) {
	args = append(args,
		slog.String("bet_external_id", bet.ExternalID),
		slog.String("game_external_id", bet.Game.ExternalID),
		slog.String("user_external_id", bet.User.ExternalID),
		// slog.String("bet_game_name", *bet.Game.Name),
	)
	slog.Log(context.Background(), level, msg, args...)
}

func (h *BetMessageHandler) SetBetSimulator(betSimulator domain.BetSimulatorService) {
	h.betSimulator = betSimulator
}

func (h *BetMessageHandler) UpdateSimulationConfig(config domain.SimulationConfig) error {
	if h.betSimulator == nil {
		return nil
	}

	h.simulationConfig = config

	return nil
}
