package websocket

import (
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
)

type WebSocketConn interface {
	ReadMessage() (messageType int, p []byte, err error)
	WriteMessage(messageType int, data []byte) error
	Close() error
}

// SetupWebSocketRoutes sets up the routes for WebSocket connections.
func SetupWebSocketRoutes(app *fiber.App, betHub *BetHub, balanceHub *BalanceHub, vipHub *VIPHub) {
	app.Get("/ping", func(c *fiber.Ctx) error {
		return c.SendString("pong")
	})

	app.Use("/ws", func(c *fiber.Ctx) error {
		if websocket.IsWebSocketUpgrade(c) {
			return c.Next()
		}

		return fiber.ErrUpgradeRequired
	})

	app.Get("/ws/v1/bets", websocket.New(func(conn *websocket.Conn) {
		handleMessages(conn, betHub, "bets", "")
	}))

	app.Get("/ws/v1/balances", websocket.New(func(conn *websocket.Conn) {
		ownerID := conn.Query("ownerId")
		if ownerID == "" {
			errMsg := "Owner ID not provided"
			slog.Error(errMsg)

			if err := conn.WriteMessage(websocket.TextMessage, []byte(errMsg)); err != nil {
				slog.Error("Failed to write error message to WebSocket", slog.Any("error", err))
			}

			return
		}

		handleMessages(conn, balanceHub, "balances", ownerID)
	}))

	 app.Get("/ws/v1/vip", websocket.New(func(conn *websocket.Conn) {
        ownerID := conn.Query("ownerId")
		slog.Info("Owner ID coming from the vip tier status websocket", slog.String("owner_id", ownerID))
        if ownerID == "" {
            errMsg := "Owner ID not provided"
            slog.Error(errMsg)
            if err := conn.WriteMessage(websocket.TextMessage, []byte(errMsg)); err != nil {
                slog.Error("Failed to write error message to WebSocket", slog.Any("error", err))
            }
            return
        }

        handleMessages(conn, vipHub, "vip", ownerID)
    }))
}

func handleMessages[M, R any](
	conn *websocket.Conn,
	hub *WebsocketHub[M, R],
	messageType string,
	ownerID string,
) {
	defer conn.Close()

	clientID, messages := hub.RegisterClient(ownerID)
	defer hub.UnregisterClient(ownerID, clientID)

	done := make(chan struct{})

	go handleClientClose(conn, done)

	slog.Debug(
		"Starting to handle WebSocket messages",
		slog.String("type", messageType),
		slog.Uint64("client_id", uint64(clientID)),
		slog.String("owner_id", ownerID),
	)

	if err := handleIncomingMessages(conn, clientID, messages, done); err != nil {
		slog.Error(
			"Failed to handle WebSocket messages",
			slog.Any("error", err),
			slog.String("type", messageType),
			slog.Uint64("client_id", uint64(clientID)),
			slog.String("owner_id", ownerID),
		)

		return
	}

	slog.Debug(
		"Handling of WebSocket messages finished",
		slog.String("type", messageType),
		slog.Uint64("client_id", uint64(clientID)),
		slog.String("owner_id", ownerID),
	)
}

func handleIncomingMessages[R any](
	conn WebSocketConn,
	clientID uint,
	messages <-chan R,
	done <-chan struct{},
) error {
	// Timer for keeping the conecction alive.
	// Some clients seem to disconnect after a while.
	slog.Info("Starting keepalive timer for WebSocket connection", slog.Uint64("client_id", uint64(clientID)))
	keepalive := time.NewTicker(10 * time.Second)
	defer keepalive.Stop()

	for {
		select {
		case message, ok := <-messages:
			slog.Info("Checking if message is ok", slog.Any("ok", ok))
			if !ok {
				slog.Info("Messages channel closed")
				return errors.New("messages channel closed")
			}
			slog.Debug("Conn is", slog.Any("conn", conn))

			slog.Debug(
				"Received message in WebSocket handler",
				slog.Any("update", message),
				slog.Uint64("client_id", uint64(clientID)),
			)

			msg, err := json.Marshal(message)
			if err != nil {
				slog.Info("Failed to marshal message", slog.Any("error", err))
				return fmt.Errorf("marshal message: %w", err)
			}

			if err := conn.WriteMessage(websocket.TextMessage, msg); err != nil {
				slog.Info("Failed to write message to WebSocket", slog.Any("error", err))
				return fmt.Errorf("write message to WebSocket: %w", err)
			}

			slog.Debug("Successfully sent message to client", slog.Uint64("client_id", uint64(clientID)))

		case <-keepalive.C:
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				slog.Info("Failed to write ping to WebSocket", slog.Any("error", err))
				return fmt.Errorf("write ping to WebSocket: %w", err)
			}
		case <-done:
			slog.Info("Client has closed the connection")
			return nil
		}
	}
}

func handleClientClose(conn *websocket.Conn, done chan<- struct{}) {
	defer close(done)

	for {
		if _, _, err := conn.ReadMessage(); err != nil {
			if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
				slog.Info("Client has sent a close message", slog.Any("error", err))
			} else {
				slog.Error("Failed to read message", slog.Any("error", err))
			}

			return
		}
	}
}