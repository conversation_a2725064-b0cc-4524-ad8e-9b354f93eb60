package websocket

import (
	"log/slog"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/api"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
)

type (
	BetHub     = WebsocketHub[domain.Bet, api.BetResponse]
	BalanceHub = WebsocketHub[domain.Balance, api.BalanceResponse]
    VIPHub     = WebsocketHub[domain.VIPStatusUpdate, api.VIPStatusResponse] 
)

func NewBetHub(bets chan domain.Bet) *BetHub {
	return NewWebsocketHub[domain.Bet, api.BetResponse](bets)
}

func NewBalanceHub(balances chan domain.Balance) *BalanceHub {
	return NewWebsocketHub[domain.Balance, api.BalanceResponse](balances)
}

func BetTransformFunc(bet domain.Bet) (string, api.BetResponse) {
	return "", api.BetToBetResponse(bet, false, false) // Empty string as ownerID for bets
}

func BalanceTransformFunc(balance domain.Balance) (string, api.BalanceResponse) {
	return balance.UserExternalID, *api.BalanceToBalanceResponse(&balance)
}

func NewVIPHub(vipUpdates chan domain.VIPStatusUpdate) *VIPHub {
    return NewWebsocketHub[domain.VIPStatusUpdate, api.VIPStatusResponse](vipUpdates)
}

// Add transform function
func VIPTransformFunc(update domain.VIPStatusUpdate) (string, api.VIPStatusResponse) {
    slog.Info("VIPTransformFunc called with update",
        slog.String("user_id", update.UserID),
        slog.String("old_tier", update.OldVIPTier),
        slog.String("new_tier", update.NewVIPTier),
        slog.Float64("bonus", update.BonusAmount),
        slog.Time("updated_at", update.UpdatedAt))

    response := api.VIPStatusResponse{
        OldTier: update.OldVIPTier,
        NewTier: update.NewVIPTier,
        Bonus:   update.BonusAmount,
        Time:    update.UpdatedAt,
    }

    slog.Info("VIPTransformFunc producing response",
        slog.String("user_id", update.UserID),
        slog.Any("response", response))

    return update.UserID, response
}