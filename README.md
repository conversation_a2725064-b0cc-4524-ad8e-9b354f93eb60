# Community service

## Building and running

## Manually

```sh
# Build & run
go run cmd/<APP>/main.go

# Build
go build cmd/<APP>/main.go
```

## Using Docker compose

The Docker compose method is intended for local development, it runs local versions of all the
dependencies.

Particularly, for Kafka we use a local cluster + Kafka UI which allows you to inject events
manually, among other things. For AWS, we use LocalStack, currently the only AWS service we use is
the secrets manager. And lastly, for Postgres, we launch the DB as a container.

```sh
# Build the community services
docker compose build

# Start all services and its dependencies
docker compose up

# Stop and remove services including their volumes
docker compose down -v
```

## Generating testify mocks

```sh
mockery
```

## Generating Swagger documentation

```sh
./scripts/generate-swagger.sh
```

## Testing

Run tests:

```sh
go test -v ./...
```

## Configuration

See configuration files in `./config/<ENVIRONMENT>` where environment is `local`, `staging`, `uat` or `production`.

## Database relational schema

We use PostgreSQL for storing thee event's data, this is the current schema:

![](./docs/db_schema.png)

# Services

![](./docs/arch_diagram.png)

## Events service

This Go module provides a Kafka consumer implementation designed to connect to a Kafka cluster, consume messages from a specified list of topics, and process them. It uses Confluent's Kafka Go client and a Postgres DB to store the information after being processed depending on the system requierement.

This module is also in charge of sending the messages to the WebSockets:
- `<community-base-URL>/ws/v1/bets`: to send information to the subscribers about the bets that are happening in real time.
- `<community-base-URL>/ws/v1/balances`: To inform about balance modification for a specific user while the user is betting in a casino game. 

## REST service

This service is intented to receive and respond to the FE requests, it uses a REST API to serve the endpoints and also uses one Postgres DB to Get, Update and create data there. 

The REST API is documented using Swagger and can be found here: 

`<commnuity-base-url>/api/v1/swagger/index.html`


## Greco service

Greco is an external tool widely used to detect bonus abuse. Community has an integration with greco in WIP status. The main idea is to subscribe to the transactions and bonuses topics (same one used in the Events service) to send that information to Greco. 

This service is intended to consume information from the source topics and to produce that information in the Greco topics (Bonus and bets) following [this integration guide](https://greco.gitbook.io/integration-guide).

## Events load test

Our most critical integration is the one with the source kafka topics and that was the main reason to develop a module specifically dedicated to load test this integration

[Check the Load test documentation here](./cmd/events_load_test/README.md)

# Key Features

##  Ranking System:

All ranking system are calculated on real time on every query that needs to have a rank.
For that we use the "RANK() OVER" SQL operation over the specific field that we want a rank from.

## Coins feature

Coins are calculated in a materialized view called `users_coins_summary`, the coins count gets calculated using the total wagered amount of the user and the boosts used by each user.

a bet should award (normally bet_amount*0.1) and look if there is any boost available to change the coin award ratio.

## Boost system

We have 3 endpoints involved in this process:
1. `/internal/v1/boost` that receives the body with the boost definition and creates a boost for a user. This endpoint is not exposed publicly.

```
curl --location '<community-base-url>/internal/v1/boost' \
--header 'Content-Type: application/json' \
--data '{
"bonus_finishes_at": "2024-08-20T00:00:00Z",
"bonus_starts_at": "2024-08-18T05:56:00Z",
"boost_duration_hours": 3,
"multiplier": 2,
"users": [
    "2fe32bb7-ca8a-4847-9238-9adbbe648b99",
    "2fe32bb7-ca8a-4847-9238-9adbbe648b98",
    "2fe32bb7-ca8a-4847-9238-9adbbe648b97"
]
}'
```

2. `/boost/users/:id` that looks if the user has a boost that could be activated taking into account the userId and the current date, the boost needs to be created using the endpoint in the step 1 and the current date has to be between the start and end date of the created boost (in point 1).

```
curl --location '<community-base-url>/api/v1/boost/users/2fe32bb7-ca8a-4847-9238-9adbbe648b99'
```

3. `/auth/boost/users/:id` it activates the boost for the user.

```
curl --location --request PATCH '<community-base-url>/api/v1/auth/boost/users/2fe32bb7-ca8a-4847-9238-9adbbe648b34'
```

4. In the materialized view `users_coins_summary` we calculate the coins based on bets table and boosts, taking into account if the bet was made in a boosting period for the user.

## VIP users

Some users' bets are manually added using an internal endpoint, these users aren't being tracked by
the external provider.

To create a new VIP user balance, do:

```sh
curl \
    --location \
    --header 'Content-Type: application/json' \
    --data '{ \
        "user_external_id": "${USER_EXTERNAL_ID}", \
        "handle": ${HANDLE}, \
        "coins": ${COINS}, \
        "bets_won": ${BETS_WON}, \
        "total_bets": ${TOTAL_BETS}, \
        "source": ${SOURCE} \
    }' \
    ${PRIVATE_IP}:${PORT}/internal/v1/vipUserBalances
```

These "VIP users" can also have their tier bumped manually, this is done by adding the necessary
coins that are required for the desired tier. These coins are added as "claimed" so they only matter
for bumping the tier status.

To bump a VIP user, do:

```sh
curl \
    --location \
    --header 'Content-Type: application/json' \
    --data '{ \
        "vip_status_to_bump": "Silver", \
        "add_base": false, \
        "source": "Bumping to silver" \
    }' \
    ${PRIVATE_IP}:${PORT}/internal/v1/users/${USER_ID}/vipStatus/bump
```

Note: if `add_base` is `false`, then we add the required coin difference for bumping the user to the
new tier. Otherwise, we add the new tier coin value to the total coins the user already has.

## Games database

We maintain a `games` table for efficient access, this table is populated using an internal
endpoint. This endpoint is invoked using an AWS scheduled task, to run it manually do:

```sh
curl -X POST ${PRIVATE_IP}:${PORT}/internal/v1/games/update
```

### Usage internal endpoints

To use internal endpoint you have to obtain the _private IP_ address of the instance of the
environment the you want to use.

This can be done by login in on AWS and then go to:
    Elastic Container Service -> <CLUSTER> -> <SERVICE> -> Tasks

And then, select the desired Task, you'll see the private IP in the configuration section.
