package main

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"gorm.io/gorm"

	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/api"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/domain"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/kafka"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/repository/postgres"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/rest_client"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/service"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/utils"
	"github.com/Monkey-Tilt/monketilt/Golang/community/internal/websocket"
	"github.com/google/uuid"
)

func main() {
	validator := validator.New()

	config, err := utils.LoadEventsServiceConfig(validator)
	if err != nil {
		utils.LogFatal("Failed to load configuration", err)
	}
	restConfig, err := utils.LoadRESTServiceConfig(validator)
	if err != nil {
		utils.LogFatal("Failed to load configuration", err)
	}

	utils.InitializeLogger(config.LogLevel)

	db, err := postgres.ConnectToDB(&config.Postgres)
	if err != nil {
		utils.LogFatal("Failed to connect to the database", err)
	}

	if os.Getenv("DATABASE_MIGRATION") == "true" {
		slog.Info("Performing database migration")
		if err := postgres.AutoMigrate(db); err != nil {
			utils.LogFatal("Failed to perform database migration", err)
		}
		slog.Info("Database migration completed")
	}

	app := api.NewApp()
	app.Use(cors.New())

	bets := make(chan domain.Bet, 1000)
	balances := make(chan domain.Balance, 1000)
	vipUpdates := make(chan domain.VIPStatusUpdate, 1000)

	transRepository := postgres.NewTransactionRepository(db)

	betHub := websocket.NewBetHub(bets)
	balanceHub := websocket.NewBalanceHub(balances)
	vipHub := websocket.NewVIPHub(vipUpdates)
	httpClient := &http.Client{}
	directusClient := rest_client.NewDirectusCmsClient(httpClient, restConfig)
	middleware := rest_client.NewRestClientMiddleware(restConfig)
	wageringClient := rest_client.NewElantilWageringClient(httpClient, restConfig, *middleware, transRepository)

	// Start all hubs
	go func() {
		slog.Info("Starting BetHub")
		betHub.Start(websocket.BetTransformFunc)
	}()

	go func() {
		slog.Info("Starting BalanceHub")
		balanceHub.Start(websocket.BalanceTransformFunc)
	}()

	go func() {
		slog.Info("Starting VIPHub")
		vipHub.Start(websocket.VIPTransformFunc)
	}()

	slog.Info("All hubs started")

	websocket.SetupWebSocketRoutes(app, betHub, balanceHub, vipHub)

	// Create Kafka consumers
	kafkaConsumers, err := initRepositoriesAndKafkaConsumers(&config.KafkaConsumer, db, bets, balances, vipUpdates, directusClient, wageringClient, config)
	if err != nil {
		utils.LogFatal("Failed to create Kafka consumers", err)
	}

	// Start HTTP server
	go func() {
		if err := app.Listen(config.Address); err != nil {
			utils.LogFatal("Failed to start the HTTP server", err)
		}
	}()

	// Start all Kafka consumers
	for _, consumer := range kafkaConsumers {
		go consumer.Start()
	}
	slog.Info("Kafka consumers started")

	// Wait for shutdown signal
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)

	<-signals
	slog.Info("Shutdown signal received, starting graceful shutdown")

	betHub.Stop()
	balanceHub.Stop()
	vipHub.Stop()
	slog.Info("Hubs stopped")

	slog.Info("Shutting down bet message handlers")
	for _, consumer := range kafkaConsumers {
		handlers := consumer.GetHandlers()
		for _, handler := range handlers {
			if bmh, ok := handler.(*service.BetMessageHandler); ok {
				bmh.Shutdown()
				slog.Info("BetMessageHandler shut down")
			}
		}
	}

	for _, consumer := range kafkaConsumers {
		if err := consumer.Stop(); err != nil {
			slog.Error("Failed to stop Kafka consumer", slog.Any("error", err))
		}
	}
	slog.Info("Kafka consumers stopped")

	if err := app.Shutdown(); err != nil {
		slog.Error("Failed to stop the HTTP server", slog.Any("error", err))
	}
	slog.Info("HTTP server stopped")
}

func initRepositoriesAndKafkaConsumers(
	config *utils.KafkaConsumerConfig,
	db *gorm.DB,
	hubBets chan domain.Bet,
	hubBalances chan domain.Balance,
	hubVIPUpdates chan domain.VIPStatusUpdate,
	directusClient domain.DirectusCMSClient,
	wageringClient domain.ElantilWageringClient,
	eventsConfig *utils.EventsServiceConfig,
) ([]*kafka.Consumer, error) {
	var consumers []*kafka.Consumer

	// Cleanup function in case of error
	cleanup := func() {
		for _, consumer := range consumers {
			if err := consumer.Stop(); err != nil {
				slog.Error("Failed to stop consumer during cleanup", slog.Any("error", err))
			}
		}
	}

	// Initialize repositories
	betRepo := postgres.NewBetRepository(db, service.VIPTiers)
	boostRepo := postgres.NewBoostRepository(db)
	gameRepo := postgres.NewGameRepository(db)
	userRepo := postgres.NewUserRepository(db, service.VIPTiers, wageringClient)
	transRepo := postgres.NewTransactionRepository(db)
	bonusTemplateRepo := postgres.NewBonusTemplateRepository(db)
	priceStore := service.NewPriceStore(wageringClient)
	priceStore.StartFiatPriceUpdater()

	betSimulatorService := service.NewBetSimulatorService(hubBets, betRepo, gameRepo)

	rdb := utils.ConnectToRedis()
	betSimulatorService.SetRedisClient(rdb)

	// Initialize VIP Status Handler
	vipStatusHandler := service.NewVIPStatusHandler(
		userRepo,
		service.VIPTiers,
		directusClient,
		betRepo,
		wageringClient,
	)
	service.StartTierUpgradeProcessor(vipStatusHandler)

	// Sessions Consumer
	sessionsHandlers := kafka.HandlersMap{
		kafka.TopicOwnerSessionsChanged: []domain.MessageHandler{
			service.NewUserMessageHandler(userRepo),
		},
	}
	sessionsConsumer, err := kafka.NewConsumer(config, sessionsHandlers)
	if err != nil {
		cleanup()
		return nil, fmt.Errorf("create sessions consumer: %w", err)
	}
	consumers = append(consumers, sessionsConsumer)

	betHandler := service.NewBetMessageHandler(
		betRepo,
		gameRepo,
		userRepo,
		boostRepo,
		hubBets,
		wageringClient,
		priceStore,
	)

	betHandler.SetBetSimulator(betSimulatorService)

	if eventsConfig.BetSimulation.Enabled {
		var games []domain.SimulatedGameData
		for _, g := range eventsConfig.BetSimulation.Games {
			id, err := uuid.Parse(g.ID)
			if err != nil {
				continue
			}
			games = append(games, domain.SimulatedGameData{
				ID:             id,
				CMSGameID:      g.CMSGameID,
				ExternalID:     g.ExternalID,
				Name:           g.Name,
				Slug:           g.Slug,
				ThumbnailID:    g.ThumbnailID,
				LossRate:       g.LossRate,
				WinMultipliers: g.WinMultipliers,
				WinWeights:     g.WinWeights,
			})
		}

		simCfg := domain.SimulationConfig{
			Enabled:               true,
			NewBetIntervalRange:   eventsConfig.BetSimulation.NewBetIntervalRange,
			HighPayoutThreshold:   eventsConfig.BetSimulation.HighPayoutThreshold,
			HighPayoutProbability: eventsConfig.BetSimulation.HighPayoutProbability,
			UsernameList:          eventsConfig.BetSimulation.Usernames,
			VipTiers:              eventsConfig.BetSimulation.VipTiers,
			Games:                 games,
			ActiveUserQueueSize:   eventsConfig.BetSimulation.ActiveUserQueueSize,
		}

		if err := betHandler.UpdateSimulationConfig(simCfg); err != nil {
			slog.Error("Failed to update bet handler slated bet simulation config", "error", err)
		}

		if err := betSimulatorService.StartSimulation(context.Background(), simCfg); err != nil {
			slog.Error("Failed to start slated bet simulation", "error", err)
		} else {
			slog.Info("Slated bet simulation started", "config", simCfg)
		}
	}

	// Transactions Consumer
	transactionsHandlers := kafka.HandlersMap{
		kafka.TopicTransactionChanged: []domain.MessageHandler{
			betHandler,
			service.NewTransactionMessageHandler(transRepo, userRepo, bonusTemplateRepo, wageringClient, priceStore),
		},
	}
	transactionsConsumer, err := kafka.NewConsumer(config, transactionsHandlers)
	if err != nil {
		cleanup()
		return nil, fmt.Errorf("create transactions consumer: %w", err)
	}
	consumers = append(consumers, transactionsConsumer)

	// Wallets Consumer
	walletsHandlers := kafka.HandlersMap{
		kafka.TopicOwnerWalletsChanged: []domain.MessageHandler{
			service.NewBalanceMessageHandler(hubBalances),
		},
	}
	walletsConsumer, err := kafka.NewConsumer(config, walletsHandlers)
	if err != nil {
		cleanup()
		return nil, fmt.Errorf("create wallets consumer: %w", err)
	}
	consumers = append(consumers, walletsConsumer)

	return consumers, nil
}
